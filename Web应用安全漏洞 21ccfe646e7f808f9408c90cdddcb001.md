# Web应用安全漏洞

请分析文档中列出的各种Web安全漏洞类型，并为每种漏洞提供具体的渗透测试方法和工具建议。

具体要求：

1. 首先完整读取并分析该文档的内容，识别其中涉及的所有Web安全漏洞类型
2. 针对每种漏洞类型，提供以下信息：
    - 漏洞的基本原理和危害
    - 推荐的渗透测试工具（如Burp Suite Pro、Nessus等）
    - 黑盒测试角度具体的测试步骤和方法
    - 检测该漏洞的有效载荷（payload）示例
    - 防护和修复建议
3. 从上到下的顺序进行分类整理

请以结构化的方式呈现结果，便于理解和实际应用。

# Web应用安全漏洞

njweb应用安全漏洞已更新到V1.5，请使用最新的漏洞库。

| [**Web应用安全漏洞V1.4.5.xlsx**](https://www.notion.so/download/attachments/94572633/Web%E5%BA%94%E7%94%A8%E5%AE%89%E5%85%A8%E6%BC%8F%E6%B4%9EV1.4.5.xlsx?version=3&modificationDate=1635126523000&api=v2) | [**Web应用安全漏洞V1.4.6.xlsx**](https://www.notion.so/download/attachments/94572633/Web%E5%BA%94%E7%94%A8%E5%AE%89%E5%85%A8%E6%BC%8F%E6%B4%9EV1.4.6.xlsx?version=1&modificationDate=1645494311000&api=v2) | [**Web应用安全漏洞V1.5.0.xlsx**](https://www.notion.so/download/attachments/94572633/Web%E5%BA%94%E7%94%A8%E5%AE%89%E5%85%A8%E6%BC%8F%E6%B4%9EV1.5.0.xlsx?version=2&modificationDate=1667456325000&api=v2) | [**JWT漏洞总结.docx**](https://www.notion.so/download/attachments/94572633/JWT%E6%BC%8F%E6%B4%9E%E6%80%BB%E7%BB%93.docx?version=2&modificationDate=1699859727000&api=v2) |
| --- | --- | --- | --- |

下面为漏洞库的在线版，供大家

| **威胁面** | **威胁分类** | **编号** | **威胁来源** | **漏洞名称** | **威胁场景** | **向量字符串** | **CVSS 3.0 Base Score(new)** | **漏洞等级** | **风险分析** | **解决方案** |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Web平台方面 | WebServer | A01 | 配置部署 | WebServer版本信息泄漏 | 返回包头部 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N | 5.3 | Medium | WebServer版本信息泄露，攻击者可根据相关WebServer版本漏洞信息，为进一步攻击做准备。 | 若不必要，建议不返回http头部中的server信息。 |
| Web平台方面 | WebServer | A02 | 配置部署 | Tomcat 默认部署页面 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:N/A:N | 4.3 | Low | 攻击者在暴力破解tomcat用户名和密码后可上传war格式的webshell | 后台使用强密码
删除Tomcat下的manager文件夹 |
| Web平台方面 | WebServer | A03 | 应用代码 | 解析漏洞 | 解析问题-Nginx：1.jpg/1.php解析问题-Apache：a.jsp.abc；phtml、pht、php3、php4和php5；.htaccess文件解析问题-IIS：1.asp;2.jpg  ；  1.asp%00.jpg； a.asp/1.jpg | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 9.1 | Critical | 利用该漏洞，攻击者可以将绕过文件类型限制，将上传的恶意文件作为PHP，JSP，ASP等格式的文件解析，来获取到一个WebShell。 | 1. .修改php.ini文件，将cgi.fix_pathinfo的值设置为0;2.在Nginx配置文件中添加以下代码：　　if ( $fastcgi_script_name ~ ..*/.*php ) {　　return 403;　　}3.升级Apache 到最新版本4.升级IIS 到最新版本 |
| Web平台方面 | WebServer | A04 | 应用代码 | IIS 短文件名猜解漏洞 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N | 5.3 | Medium | 攻击者可通过暴力猜解来得到网站目录下面所有文件名称，了解整个网站目录结构，为下一步攻击做准备。 | 1.CMD关闭NTFS 8.3文件格式的支持2.修改注册表禁用短文件名功能3.关闭Web服务扩展- ASP.NET4.升级netFramework至4.0以上版本 |
| Web平台方面 | WEB应用框架 | A05 | 应用代码 | CMS 漏洞 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:L/A:L | 7.4 | High | 直接利用开源模板或二次开发，可能存在cms自身漏洞。 | 定期检查开源模板的版本，及时升级及安装安全补丁 |
| Web平台方面 | WEB应用框架 | A06 | 应用代码 | 编辑器文件上传 | 常见编辑器漏洞 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 9.1 | Critical | 攻击者利用各类Editor（如FCKeditor，Ewebeditor,Kindeditor等）文件上传漏洞，上传webshell，控制操作系统，执行操作系统命令。 | 定期检查开源模板的版本，及时升级及安装安全补丁 |
| 程序安全功能设计方面 | 身份认证 | C01 | 应用代码 | 应用系统弱口令 | 暴力破解成功 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:L | 7.6 | High |  | 1. 设置强的密码策略（长度至少8位，包括大、小写字母、特殊字符和数字中的至少三类）防止弱口令风险。2. 强制要求客户使用强密码策略，不允许用户自行修改密码为弱密码。 |
| 程序安全功能设计方面 | 身份认证 | C02 | 应用代码 | 弱密码策略 | 修改密码模块允许弱密码 | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:N/A:L | 3.5 | Low | 应用系统未强制使用强密码策略，导致用户可自行设置弱口令，增加了账号密码被暴破的可能。 | 1. 设置强的密码策略（长度至少8位，包括大、小写字母、特殊字符和数字中的至少三类）防止弱口令风险。2 强制要求客户使用强密码策略，不允许用户自行修改密码为弱密码。 |
| 程序安全功能设计方面 | 身份认证 | C03 | 应用代码 | 暴力破解 | 缺少验证码机制 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:L | 5.4 | Medium | 系统登录模块缺少验证码机制，攻击者可以采取暴力破解的方式，穷举获取正确结果。 | 1.注册或登录时，客户发送请求敏感信息进行加密，例如密码、电话、邮件等敏感信息进行加密传输。2.在登录模块设置验证码验证、错误次数限制，且设置图片验证码为一次一验。3.设置强的密码策略（长度至少8位，包括大、小写字母、特殊字符和数字中的至少三类）防止弱口令风险。 |
| 程序安全功能设计方面 | 身份认证 | C04 | 应用代码 | 缺少附加验证 | 缺少验证码机制 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:L | 5.4 | Medium | 系统重要功能模块（如修改密码，付款）缺少验证码机制，攻击者可以采取暴力破解的方式，穷举获取正确结果。 | 重要模块附加二次验证，可以是图片验证码或者短信验证等。 |
| 程序安全功能设计方面 | 身份认证 | C05 | 应用代码 | 登录服务拒绝 | 具有锁定机制，但不具有图片验证码时造成大量账号锁定 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:H | 7.1 | High | 登录模块缺少验证码机制，但对单个账号具有锁定机制，攻击者通过耗尽单个用户登录次数的方式遍历用户名达到大面积锁定用户的结果，造成正常用户被锁定无法登录。 | 登录模块设置图片验证码，且设置图片验证码为一次一验。 |
| 程序安全功能设计方面 | 身份认证 | C06 | 应用代码 | 图片验证码缺陷 | 空值，空值空参数，任意值绕过/形同虚设，不校验；或先验证密码/重复使用/复杂度低 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N | 5.4 | Medium | 验证码模块存在缺陷，可能被绕过，继而进行暴力破解攻击。 | 完善图片验证码的校验逻辑。 |
| 程序安全功能设计方面 | 身份认证 | C07 | 应用代码 | 用户名枚举 |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:N | 3.1 | Low | 攻击者可以利用此缺陷，暴力穷举系统中真实存在的用户名，为进一步攻击做准备 | 增加图片验证码模块，避免用户名被暴力穷举。 |
| 程序安全功能设计方面 | 访问控制 | C08 | 应用代码 | 目录列表 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N | 8.1 | High | Web Server开启了目录列表，攻击可以遍历Web应用程序根目录下的所有目录及文件，可能造成敏感文件被下载。 | 1.如果必须开启该目录的目录列表功能，则应对该目录下的文件进行详细检查，确保不包含敏感文件。2.如非必要，请重新配置WEB服务器，禁止该目录的自动目录列表功能。 |
| 程序安全功能设计方面 | 访问控制 | C09 | 应用代码 | 未授权访问 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | 系统资源被访问时，未验证当前用户，使得攻击者未经授权访问系统资源。 | 设置合理的访问控制策略，所有访问系统的请求，首先验证是否是合法有效用户。如不是则跳转到登录页面。 |
| 程序安全功能设计方面 | 访问控制 | C10 | 应用代码 | 横向越权 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:N | 7.1 | High | 系统资源被访问时，未验证当前用户的身份，使得攻击者越权访问其它同级别用户的系统资源。 | 完善相关模块的访问控制，请求资源时，对用户是否拥有权限进行判断。
对数据设置权限标识 |
| 程序安全功能设计方面 | 访问控制 | C11 | 应用代码 | 纵向越权 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:L | 7.6 | High | 系统资源被访问时，未验证当前用户的身份，使得低权限用户可以越权访问更高权限用户的系统资源。 | 完善相关模块的访问控制，请求资源时，对用户是否拥有权限进行判断。
用户与角色对数据有映射关系 |
| 程序安全功能设计方面 | 配置错误 | C12 | 应用代码 | 任意文件下载 | /WEB-INF/web.xml/WEB-INF/config/jdbc.properties../../../etc/passwd | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N | 8.1 | High | 攻击者通过遍历文件ID或者文件相对（绝对）路径的方式，任意下载系统文件。 | 1.禁止用文件名的方式访问网站目录的文件。2.访问的文件名必须限制在规定的目录内，禁止越权使用别的目录3.在下载前对传入的参数进行过滤，直接将..替换成其他字符。4.对待下载文件类型进行检查，判断是否允许下载类型。 |
| 程序安全功能设计方面 | 会话管理 | C13 | 应用代码 | 会话标识泄露 | 公共功能的URL包含会话token，例如分享到朋友圈 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | 系统部分对外共享的功能模块未考虑周全，将会话token直接存放在共享的URL中造成系统会话凭证泄露 | 避免将会话tonken直接存放在URL中 |
| 程序安全功能设计方面 | 会话管理 | C14 | 应用代码 | 会话标识泄露 | 普通受限的URL中包含会话或token | CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:L/A:N | 5.5 | Medium | 很多WEB开发语言为了防止浏览器禁止了Cookie而无法识别用户，允许在URL中携带sessionid或用于身份认证的token，这样虽然方便，但容易泄露在代理服务器上或者服务器的日志文件中，并有可能引起钓鱼的安全漏洞。通常会让攻击者假冒受害用户非法进入应用系统。 | 在Cookie中保存sessionid，不得在URL中携带sessionid或用于身份认证的token |
| 程序安全功能设计方面 | 会话管理 | C15 | 应用代码 | 会话标识未更新 |  | CVSS:3.0/AV:N/AC:H/PR:H/UI:N/S:U/C:H/I:L/A:N | 5 | Medium | 登录前后会话标示未更新，攻击者可能会窃取或操纵客户会话和 Cookie，它们可能用于模仿合法用户，从而使黑客能够以该用户身份查看或变更用户记录以及执行事务。 | 始终生成新的会话，供用户成功认证时登录。防止用户操纵会话标识。 |
| 程序安全功能设计方面 | 会话管理 | C16 | 应用代码 | 无效的会话登出 |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:L | 4.2 | Medium | 用户注销登录后，服务器端未立刻注销原会话信息，攻击者可继续使用原会话信息访问系统资源。 | 用户注销时，在服务器端注销该会话，使其失效。 |
| 程序安全功能设计方面 | 会话管理 | C17 | 配置部署 | 会话超时设置 |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:L | 4.2 | Medium | 系统未设置单次会话存在的最大有效时间（或最大有效时间过长），增加了会话凭证被泄露后的利用时间窗口。（注：Weblogic环境下需要在war中被设置好） | 设置会话超时时间（重要系统为15分钟,非重要系统30分钟） |
| 程序安全功能设计方面 | 会话管理 | C18 | 应用代码 | 会话标识可预测 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | 攻击者采集大量会话ID，分析会话ID生成规律，采取预测的方式生成虚拟会话ID试图登录系统。 | 1.用随机会话令牌，会话ID或发给客户端的Cookie 应该是不容易预测的（不使用基于可预测变数（如客户端IP 地址）的线性算法） 。2.会话ID至少有50个字符的长度。3.设置会话令牌超时时间。 |
| 程序安全功能设计方面 | 会话管理 | C19 | 应用代码 | Cookie中泄露敏感信息 | Cookie中泄露敏感信息 | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:N/A:N | 5.3 | Medium | Cookie中包含的敏感信息可能会被攻击者窃取，如用户名、密码。 | Cookie中与用户身份，权限相关的参数值应避免以明文序列的形式存在。 |
| 程序安全功能设计方面 | 输入验证 | C20 | 应用代码 | 跨站请求伪造(CSRF) |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:R/S:U/C:L/I:H/A:N | 5.4 | Medium | 攻击者可以构造仅高权限用户才能够进行操作的请求，诱使登录状态的高权限用户点击伪造的链接、图片，从而成功执行非法的请求，以用来执行重要的查看，修改，删除，授权等恶意操作。 | 1. 每个请求都应该包含唯一标识，服务器必须检查这个参数是否符合次标识，若不符合，便废弃请求。2. 服务器对请求的来源地址增加Referer头的校验。3. 在执行关键操作时增加验证码判断程序。 |
| 程序安全功能设计方面 | 审核与日志 | C21 | 应用代码 | 日志功能泄露敏感信息 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 应用程序的日志功能中输出了敏感的系统信息，攻击者可能会窃取到这些敏感信息。 | 应用程序的日志功能中不应包含敏感信息 |
| 程序编码方面 | 输入验证 | D01 | 应用代码 | SQL注入 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L | 8.3 | High | 攻击者可以利用SQL注入漏洞，获取数据库中的信息，或通过shell进一步可能获取操作系统权限，执行操作系统命令。 | 1、参数化SQL语句（用参数化方法构建SQL语句，以防止数据库执行从用户输入插入的SQL语句。） 2、验证输入（通过“白名单”方式验证用户输入。在查询参数传入时，使用“白名单”对参数值验证，若在“白名单”的范围之内，则允许，否则提示错误。） 3、 数据库异常处理机制（发生数据库错误时，不显示完整的错误消息。可以使用正常的页面 进行响应，或将用户重定向到某特定页面。）4、针对普通的sql 增删改查应使用PreparedStatement进行预编译5、针对于框架（hibernate，mybatis）参考[https://blog.csdn.net/xinghuotianci/article/details/42678517](https://blog.csdn.net/xinghuotianci/article/details/42678517) |
| 程序编码方面 | 输入验证 | D02 | 应用代码 | 远程命令执行 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:L | 8.6 | High | 如果用户输入合并到可动态执行的代码中，那么攻击者可以提交构造的输入，破坏原有的数据，指定服务器执行构造的命令。 | 1、在进入执行命令方法之前对敏感字符做转义创建；2、只包含允许字符的“白名单“用于验证用户输入，而不在“白名单”中的字符和尚未发现的威胁都将在该名单中删除；3、Web 应用程序及其组件应该在不允许执行操作系统命令的受限权限中运行，禁用一些不必要的方法如Runtime.exec。 |
| 程序编码方面 | 输入验证 | D03 | 应用代码 | 文件包含 |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:C/C:H/I:H/A:L | 8.4 | High | 攻击者通过传递本地或者远程的文件(allow_url_fopen开启)作为参数进行利用,可以读取敏感信息、执行命令。 | php.ini 中open_basedir 限制访问在指定区域
过滤“. / \” 禁止服务器远程文件包含 |
| 程序编码方面 | 输入验证 | D04 | 应用代码 | XML注入（包含XXE） |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 如果服务端XML分析器没有进行适当的数据验证，攻击者通过设计特殊的输入，破坏应用程序的运行并执行某些未授权的操作 | 1.在用户可控的XML数据里禁止引用外部实体2.过滤用户提交的XML数据 |
| 程序编码方面 | 输入验证 | D05 | 应用代码 | JSON注入 | JSON注入是指应用程序所解析的JSON数据来源于不可信赖的数据源，程序没有对这些不可信赖的数据进行验证、过滤，
如果应用程序使用未经验证的输入构造 JSON，则可以更改 JSON 数据的语义。在相对理想的情况下，攻击者可能会插入
无关的元素，导致应用程序在解析 JSON数据时抛出异常。 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H | 8.1 | High | 攻击者可以利用JSON注入漏洞在JSON数据中插入元素，从而允许JSON数据对业务非常关键的值执行恶意操作，严重的可能导致XSS和动态解析代码。 | 检查程序逻辑，根据实际需求对数据进行合理过滤和安全校验，以避免产生JSON注入。 |
| 程序编码方面 | 输入验证 | D06 | 应用代码 | SSI注入 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 全称Server-Side Includes Injection，即服务端包含注入，SSI 是类似于 CGI，用于动态页面的指令，攻击者可以远程在 Web 应用中注入脚本来执行代码。 | 由于未对用户输入正确执行危险字符清理，可能会在 Web 服务器上运行远程命令。这通常意味着完全破坏服务器及其内容[1] 清理用户输入 － 禁止可能支持 SSI 的模式/字符。[2] 由于 SSI 会带来许多安全风险，建议您不在 Web 站点中使用 SSI。 |
| 程序编码方面 | 输入验证 | D07 | 应用代码 | XPath注入 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 如果输入未经过任何过滤或净化的输入被插入到XPath查询中，攻击者就可以通过控制查询来破坏应用程序的逻辑，或者获取未授权访问的数据。 | （一）对于出现在任何用户输入中的XML元字符进行HTML编码，常见的有：1. <  &lt;2. >  &gt;3. /   &#47;4. &  &amp;5. ‘   &apos;6. “   &quot;（二）验证输入，通过“白名单”方式控制用户只能输入简单数据，应禁止输入任何可能破坏XPath查询的字符，主要包括字符如下：1. （、）2. ＝3. ‘4. [、]5. :6. ,7. *8. / |
| 程序编码方面 | 输入验证 | D08 | 应用代码 | CSV注入 | CSV注入(CSV Injection）漏洞通常会出现在有导出文件(.csv/.xls)功能的网站中。
当导出的文件内容可控时，攻击者通常将恶意负载（公式）注入到输入字段中，用户导出文件打开后，EXCEL会调用本身的动态功能，执行攻击者的恶意代码，从而控制用户计算机。 | CVSS:3.0/AV:L/AC:L/PR:N/UI:R/S:C/C:H/I:N/A:N | 6.3 | Medium | 当导出的文件内容可控时，攻击者通常将恶意负载（公式）注入到输入字段中，用户导出文件打开后，EXCEL会调用本身的动态功能，执行攻击者的恶意代码，从而控制用户计算机。 | 确保没有任何单元格以下列任何字符开头：
等于（“ =”）
加号（“ +”）
减号（“-”）
在 （”@”）
可以在包含此类字符的单元格的开头添加撇号（’）。添加撇号（’）会告诉excel该单元格不包含公式 |
| 程序编码方面 | 输入验证 | D09 | 应用代码 | LDAP注入 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 如果用户提交的数据不经任何过滤即被插入到LDAP查询中，攻击者就可以通过提交专门设计的输入来修改过滤器的结构，以检索数据或执行未授权操作 | 1、应使用白名单的方法，确保LDAP查询中由用户控制的数值完全来自于预定的字符集合，应不包含任何LDAP元字符。如果由用户控制的数值范围要求必须包含 LDAP元字符，则应使用相应的编码机制转义这些元字符在LDAP查询中的意义。1.如&、！、|、=、<、>、，、+、-、”、’、；这些字符正常情况下不会用到，如果用户的输入中出现了，需要用反斜杠转义处理。2.有些字符如(、)、\、*、/、NUL这些字符不仅需要用反斜杠处理，还要将字符变成相应的ASCII码值 |
| 程序编码方面 | 输入验证 | D10 | 应用代码 | 服务端请求伪造(SSRF) |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:C/C:H/I:L/A:L | 7.7 | High | 攻击者可以利用服务器端跨站请求伪造漏洞，向服务器端发送指令获取内网敏感信息，或者执行部分命令。 | 1.设置白名单，只允许白名单范围内URL可进行请求2.过滤返回信息，如果web应用是去获取某一种类型的文件。那么在把返回结果展示给用户之前先验证返回的信息是否符合标准。3.统一错误信息，避免用户可以根据错误信息来判断远程服务器的端口状态。4.限制请求的端口为http常用的端口，比如，80，443，8080.80905.黑名单内网ip。避免应用被用来获取内网数据，攻击内网。6.禁用不需要的协议。仅仅允许http和https请求。可以防止类似于file://, gopher://, ftp://等引起的问题。 |
| 程序编码方面 | 输入验证 | D11 | 应用代码 | HTML注入 | 框架钓鱼/链接注入 | CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N | 5.4 | Medium | 攻击者通过插入类似<iframe><a>、<img>等标签等标签，达到修改网站主页布局或插入恶意请求链接，进而诱使受害者输入敏感信息从而窃取或者执行恶意的操作。 | 1. 输入验证
服务器端验证用户端的输入参数长度、类型是否正确，是否包含一些特殊字符（如<,>,',"等），建议过滤的常见危险字符如下（前五类建议强制性过滤，其他的可以在不影响业务的情况下选择性过滤）：1. '（单引号）2. "（引号）3. <>（尖括号）4. ()（圆括号）5. ;（分号）6. ,（逗号）7. \（反斜杠）8. %（百分比符号）9. & （& 符号）10. $（美元符号）11. @（at 符号）12. \'（反斜杠转义单引号）13. \"（反斜杠转义引号）14. +（加号）15. |（竖线符号）16. CR（回车符，ASCII 0x0d）17. LF（换行，ASCII 0x0a）2. 输出编码根据输出到HTML中的位置不同有针对性的进行合适的编码，如HTML编码、JavaScript编码、URL编码。最终做到，该数据不要超过自己所在的代码区域，不要被当做代码执行，做好数据与代码的分离。 |
| 程序编码方面 | 其他 | D12 | 应用代码 | HTTP响应分割（CRLF注入） |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:N | 4.3 | Medium | 攻击者可能注入自定义HTTP头。例如，攻击者可以注入会话cookie或HTML代码。这可能会进行类似的XSS（跨站点脚本）或会话固定漏洞。 | 过滤用户输入中的CR，LF字符（%0a，%0d） |
| 程序编码方面 | 输入验证 | D13 | 应用代码 | 文件上传-webshell | 上传asp、jsp、php文件成功且能解析执行 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 9.1 | Critical | 攻击者上传webshell控制应用程序，获取操作系统权限。 | 限制文件上传的类型，检查文件扩展名，只允许特定的文件上传。用白名单的方式而不是一个黑名单。检查双扩展，如.php.png。检查的文件没有文件 名一样。htaccess（对ASP.NET配置文件，检查网络配置。）。改变对上传文件夹的权限，文件在此文件夹中不可执行。如果可能的话，重命名上传文件。 |
| 程序编码方面 | 输入验证 | D14 | 应用代码 | 文件上传-XSS | 上传html文件成功且能弹xss | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L | 8.3 | High | 攻击者上传包含XSS代码的HTML文件，造成存储跨站。 |  |
| 程序编码方面 | 输出处理 | D15 | 应用代码 | 存储型XSS |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:L | 7.6 | High | 攻击者可以通过构造URL注入JavaScript、VBScript、ActiveX、HTML或者Flash的手段，利用跨站脚本漏洞欺骗用户，收集Cookie等相关数据并冒充其他用户。通过精心构造的恶意代码，可以让访问者访问非法网站或下载恶意木马，如果再结合其他攻击手段（如社会工程学、提权等），甚至可以获取系统的管理权限。 | 1.在表单提交或者url参数传递前，对需要的参数进行过滤,可参考XSS过滤工具类代码2.利用函数对用户输入内容容易出现xss漏洞的参数进行过滤， 检查用户输入的内容中是否有非法内容。如<>（尖括号）、”（引号）、 ‘（单引号）、%（百分比符号）、;（分号）、()（括号）、&（& 符号）、+（加号）等。
3.严格控制输出，对输出全部进行html实体编码。4.白名单：定义允许的通过字符，如只允许字母、数字等，或者引入JSOUP5.富文本处理：引入JSOUP |
| 程序编码方面 | 输出处理 | D16 | 应用代码 | 反射型XSS |  | CVSS:3.0/AV:N/AC:H/PR:L/UI:R/S:U/C:H/I:H/A:L | 6.7 | Medium | 攻击者可以通过构造URL注入JavaScript、VBScript、ActiveX、HTML或者Flash的手段，利用跨站脚本漏洞欺骗用户，收集Cookie等相关数据并冒充其他用户。通过精心构造的恶意代码，可以让访问者访问非法网站或下载恶意木马，如果再结合其他攻击手段（如社会工程学、提权等），甚至可以获取系统的管理权限。 | 1.在表单提交或者url参数传递前，对需要的参数进行过滤,可参考XSS过滤工具类代码2.利用函数对用户输入内容容易出现xss漏洞的参数进行过滤， 检查用户输入的内容中是否有非法内容。如<>（尖括号）、”（引号）、 ‘（单引号）、%（百分比符号）、;（分号）、()（括号）、&（& 符号）、+（加号）等。、严格控制输出3.白名单：定义允许的通过字符，如只允许字母、数字等，或者引入JSOUP4.富文本处理：引入JSOUP |
| 程序编码方面 | 输出处理 | D17 | 应用代码 | DOM型XSS |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:L | 6.7 | Medium | 攻击者可以通过构造URL注入JavaScript、VBScript、ActiveX、HTML或者Flash的手段，利用跨站脚本漏洞欺骗用户，收集Cookie等相关数据并冒充其他用户。通过精心构造的恶意代码，可以让访问者访问非法网站或下载恶意木马，如果再结合其他攻击手段（如社会工程学、提权等），甚至可以获取系统的管理权限。 | 1.在表单提交或者url参数传递前，对需要的参数进行过滤,可参考XSS过滤工具类代码2.利用函数对用户输入内容容易出现xss漏洞的参数进行过滤， 检查用户输入的内容中是否有非法内容。如<>（尖括号）、”（引号）、 ‘（单引号）、%（百分比符号）、;（分号）、()（括号）、&（& 符号）、+（加号）等。、严格控制输出3.白名单：定义允许的通过字符，如只允许字母、数字等，或者引入JSOUP4.富文本处理：引入JSOUP |
| 程序编码方面 | 输出处理 | D18 | 应用代码 | 敏感信息未脱敏 | 数据返回包中存在未脱敏的个人敏感信息（银行卡、身份证等） | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:L | 7.6 | High | 应用系统返回到前端的信息中包含用户个人隐私信息，例如身份证，银行卡号，手机号，真实姓名等，未进行脱敏处理，容易造成泄露。 | 1.敏感信息在存储、传输、显示时应进行安全处理，可采用的处理方式为加密或脱敏2.未使用参数直接不返回 |
| 程序编码方面 | 其他 | D19 | 应用代码 | URL重定向 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N | 5.4 | Medium | 攻击者可以利用URL重定向漏洞将系统页面重定向到钓鱼页面，进而实施钓鱼攻击。 | 1. 代码固定跳转地址，不让用户控制变量（类似短链）2. 跳转目标地址采用白名单映射机制3. 合理充分的校验跳转的目标地址，非己方地址时告知用户跳转风险 |
| 程序编码方面 | 其他 | D20 | 应用代码 | 链接未校验 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N | 5.4 | Medium | 连接校验不合理，攻击者可以诱导用户访问恶意链接地址。 | 使用白名单校验域名信息，过滤@跳转符号。 |
| 程序编码方面 | 功能滥用 | D21 | 应用代码 | 短信炸弹 | 短信接口恶意调用 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:L | 4.3 | Medium | 反馈、投诉、申请等接口的页面未限制验证码的发送频率和次数，攻击者可以利用短信接口在短时间内对指定手机号发送大量短信 | 1.为短信接口的调用设置时间间隔，设置单个用户访问该接口的频率，时间间隔60s2.限制次数，设置发送频率和次数，超过发送次数后禁止验证，次数为每个手机号30条/天，相同内容短信每个手机号6条/天 |
| 程序编码方面 | 功能滥用 | D22 | 应用代码 | 邮件炸弹 | 邮件接口恶意调用 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:L | 4.3 | Medium | 反馈、投诉、申请等接口的页面未限制验证码的发送频率和次数，攻击者可以利用邮箱接口在短时间内对指定邮箱发送大量邮件 | 1.为邮箱接口的调用设置时间间隔，设置单个用户访问该接口的频率，时间间隔60s2.限制次数，设置发送频率和次数，超过发送次数后禁止验证，次数为每个邮箱30条/天，相同内容短信6条/天 |
| 程序编码方面 | 功能滥用 | D23 | 应用代码 | 接口滥用 | 反馈、投诉、申请等接口的页面未使用图形验证码/反馈、投诉、申请等接口未限制使用频率与次数 | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:N/A:L | 3.5 | Low | 相关业务接口未使用图形验证码或调用频率，攻击者可发送大量无效的反馈，投诉，申请信息，干扰正常业务。 | 设置图片验证码，且设置图片验证码为一次一验1.设置单个用户访问该接口的频率，设置发送频率和次数，超过发送次数后禁止验证，时间间隔60s， 2.设置会话检测机制，保障该会话机制唯一性（ 每个请求都应该包含唯一标识，服务器必须检查这个参数是否符合次标识，若不符合，便废弃请求。） |
| 程序编码方面 | 利用错误消息 | D24 | 配置部署 | 应用程序错误 | 1.错误消息泄露网站根目录、执行的SQL语句，加密算法等 2.错误消息泄露简单的webServer，数据库版本，名称等 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N | 5.3 | Medium | 应用程序错误信息中泄露了相关敏感信息，攻击者为进一步攻击做准备。（注：Weblogic环境下需要在war中被设置好） | 捕获后台抛出的异常，制定异常固定跳转页面，如500错误，跳转到相应页面，例如“系统异常请与管理员联系”，403异常，找不到页面等等 |
| 程序编码方面 | 其他 | D25 | 应用代码 | 明文密码 | 登录页面密码未加密 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 7.5 | High | 明文密码可以被暴力破解和中间人嗅探（泄露密码） | 密码RSA加密或加盐的MD5 hash |
| 程序编码方面 | 其他 | D26 | 应用代码 | 弱密码算法 | 对密码使用不加盐的md5算法、SHA1、URL编码和base64编码 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N | 6.5 | Medium | 对密码使用不加盐的md5算法、SHA1、URL编码和base64编码，可解密出明文字符。 | 密码RSA加密或加盐的MD5 hash |
| 业务逻辑性方面 | 访问控制 | E1 | 应用代码 | 任意用户登录 | 万能密码，本质上是SQL注入/显式指定登录状态如isLogin=true/修改返回包状态/替换用户名，userid，手机号，邮箱等标识/手机号/短信验证码未配对校验/验证码直接出现在返回包中/短信验证码爆破（4位） | CVSS:AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:L | 9.4 | Critical | 攻击者绕过登录逻辑，非法使用任意用户账号登录应用系统，进而执行进一步的危险操作或窃取其它用户敏感信息。 | 1.使用参数化查询，避免登录过程的万能密码。2。完善应用的认证逻辑，避免通过显示指定参数值的方式指定登录状态。3.完善应用的认证逻辑，避免通过返回值的方式指定登录状态。4.服务器端验证短信码与接收该短信码的手机。5.避免验证码出现在返回包中。6。设置短信验证码的有效时间，.加强短信验证码难度（使用六位数验证码或大小写字母加数字复杂组合） |
| 业务逻辑性方面 | 访问控制 | E2 | 应用代码 | 修改任意用户密码 | 替换用户名，userid，手机号，邮箱等标识/手机号-短信验证码未配对校验/验证码直接出现在返回包中/多步骤校验绕过某些步骤/短信验证码爆破（4位） | AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:L | 9.4 | Critical | 攻击者可以修改任意用户的密码，进而登录应用系统执行进一步的危险操作或窃取其它用户敏感信息。 | 1.完善应用的”忘记密码“，”修改密码“逻辑。2.服务器端验证短信码与接收该短信码的手机。3.避免验证码出现在返回包中。4.所有验证在服务端进行，验证问题的答案不能以任何形式返回客户端中（如图片验证码答案、短信验证码、验证问题答案等）。5.验证结果及下一步跳转操作由服务端直接进行。6.应尽可能避免采用连续身份验证机制，无论采用何种验证机制，只有当所有的数据输入以后，才进行身份验证数据的验证。7.设置短信验证码的有效时间，加强短信验证码难度（使用六位数验证码或大小写字母加数字复杂组合） |
| 业务逻辑性方面 | 其他 | E3 | 应用代码 | 链接未失效 |  | AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:N/A:N | 5.9 | Medium | 用以找回密码的URL链接在修改成功后未失效，导致该URL泄露后攻击者仍可利用该URL链接继续修改密码。 | 修改密码的链接在修改成功后立即失效。 |
| 业务逻辑性方面 | 访问控制 | E4 | 应用代码 | 任意用户注册 | 替换用户名，userid，手机号，邮箱等标识/ 手机号-短信验证码未配对校验/验证码直接出现在返回包中/短信验证码爆破（4位） | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:L | 6.5 | Medium | 攻击者可以绕过注册逻辑，大量注册无用的垃圾账号，为进一步薅羊毛做准备。 | 1.完善应用的”忘记密码“，”修改密码“逻辑。2.服务器端验证短信码与接收该短信码的手机。3.避免验证码出现在返回包中。4.设置短信验证码的有效时间。5加强短信验证码难度（使用六位数验证码或大小写字母加数字复杂组合） |
| 业务逻辑性方面 | 支付类逻辑 | E5 | 应用代码 | 支付漏洞 | 直接修改金额/修改商品数量/替换商品ID | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N | 7.5 | High | 攻击者绕过支付逻辑，以非法金额完成支付，给企业造成大量金钱损失。 | 完善支付逻辑。 |
| 业务逻辑性方面 | 支付类逻辑 | E6 | 应用代码 | 非法优惠 | 越过最大/小值、数量限制/越过时间范围限制/越过套餐商品种类、数量组合/发现隐藏/下单的优惠/取消虚拟商品/优惠码可爆破 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N | 7.5 | High | 攻击者绕过相关逻辑，非法获取优惠，给企业带来金钱损失。 | 完善优惠逻辑的相关校验。 |
| 业务逻辑性方面 | 其它 | E7 | 应用代码 | 关键信息弱校验 | 修改上传的行程信息/注册已经存在的用户名/生日信息修改（生日优惠） | AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H | 8.1 | High | 攻击者上传经过修改的行程信息，欺骗服务器，进而造成总行程数，使用年限等欺骗。
攻击者通过注册已经存在的用户，覆盖原来的用户，造成原账户不可用或者获取原账号控制权。
攻击者通过修改自身生日日期，用以获取商城提供给用户的生日福利。 | 上传行程的请求包做sign签名，避免攻击者篡改。
完善用户注册模块的逻辑，避免重复注册。
相关用户个人信息，一经设置不可更改。 |
| 业务逻辑性方面 | 其他 | E8 | 应用代码 | 程序逻辑缺陷 | 越过状态限制的操作/越过客户端”可点击“限制 | AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H | 8.1 | High | 攻击者越过系统设定好的状态限制，进行非法操作或修改前端HTML元素，使原本不可点击的按钮重新可点击，进而执行相关的危险操作。 | 完善服务器端相关限制校验的逻辑。 |
| 业务逻辑性方面 | 其他 | E9 | 应用代码 | 竞争条件测试 | “竞争条件”发生在多个线程同时访问同一个共享代码、变量、文件等没有进行锁操作或者同步操作的场景中。 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:L | 8.2 | High | 攻击者通常利用多线程并发请求，在数据 库中的余额字段更新之前，多次兑换积分或购买商品，从中获得利益。 | 在处理订单、支付等关键业务时，使用悲观锁或乐观锁保证事务的 ACID特性（原子性、一致性、隔离性、持久性），并避免数据脏读（一个事务读取了另一个事务未提交的数据），解决竞争条件和并发操作可能带来的相关业务问题。 |
| 应用配置 | 应用配置管理 | B01 | 应用代码 | 敏感信息泄露 | 1.内部IP地址/源代码/账号密码（包括密码密文）2.在不需要展示敏感信息的功能里，泄露或传输敏感信息。请确保项目组充分了解哪些是敏感信息的。有时看似无害的信息对攻击者比人们意识到的要有用得多。突出这些危险有助于更安全地处理敏感信息。敏感数据泄露包括但不限于：口令、密钥、证书、License、隐私数据(如短消息的内容)、授权凭据、个人数据(如姓名、住址、电话等)等。
3.SourceMap文件泄露，js.map文件泄露 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:L | 7.6 | High | 攻击者利用注释信息中获得的内网IP信息，对内网渗透做准备。
攻击者利用注释信息中获得的源代码信息，对应用系统进行白盒代码分析，方便找出可利用漏洞。
攻击者利用注释信息中获得的登陆凭证，直接登陆应用程序。
攻击者可以针对泄露的前端源代码分析其中各种信息如隐蔽接口、API、加密算法、管理员邮箱、内部功能等等,或者接口API可以尝试未授权漏洞,拼接接口越权漏洞。 | 1.生产环境下对注释信息进行过滤，使其不包含敏感信息2.禁止使用IP链接，防止内部网络信息泄露，可以把IP链接改成域名连接方式3.生产环境下对注释信息进行过滤，使其不包含敏感信息3.禁止传输超出展示的信息
4.针对SourceMap文件泄露，禁用SourceMap 功能或限制 SourceMap 文件的访问权限。例如：
在vue.config.js里添加productionSourceMap:false 这一配置项，阻止map文件生成，或
项目路径下修改config/index.js中build对象productionSourceMap: false; |
| 应用配置 | 应用配置管理 | B02 | 应用代码 | .svn或.git文件泄露 | [http://xxxx/.svn/entries](http://xxxx/.svn/entries) | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:L/A:L | 7.6 | High | svn或git访问控制不严格，导致系统重要的文件甚至源代码泄露 | 从生产系统中删除这些文件或限制访问.svn.git目录 |
| 应用配置 | 应用配置管理 | B03 | 配置部署 | 跨域资源共享(CORS) |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:N/A:L | 3.5 | Low | 跨域配置不当导致： 1、HTTP头只能说明请求来自一个特定的域，但是并不能保证这个事实。因为HTTP头可以被伪造。 2、当调用已被入侵的第三方API时，可能连带导致本系统被入侵 3、恶意的跨域请求，访问伪造或恶意资源 4、服务器内部信息泄露 5、客户端用户信息泄露 | （1）如果没有必要就不要开启CORS（2）严格限制域白名单，而不是使用*（3）尽量避免使用Access-Control-Allow-Credentials |
| 应用配置 | 基础结构和应用管理界面 | B04 | 配置部署 | 管理后台暴露 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N | 5.4 | Medium | 应用管理后台暴露给外网攻击者，且存在暴力破解的风险。 | 1.尽量保持网站后台文件的独立性，避免跟其它网站页面有关联性。2.建议用户对网站后台采取IP授权管理，只有被认证的IP可以远程访问网站后台。3.建议用户对管理页面采取数字认证管理。4.设置图片验证码，防止暴力破解。 |
| 应用配置 | 基础结构和应用管理界面 | B05 | 应用代码 | 管理后台失陷 | 弱口令/未授权访问，直接访问 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H | 8.8 | High | 应用管理后台暴露且存在弱口令，攻击者控制管理后台，执行危险的操作或更改重要的配置。 | 1.设置强的密码策略（长度至少8位，包括大、小写字母、特殊字符和数字中的至少三类）防止弱口令风险。2.强制要求客户使用强密码策略，不允许用户自行修改密码为弱密码。 |
| 应用配置 | http方法 | B06 | 应用代码 | PUT方法上传任意文件 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 9.1 | Critical | WebDAV是HTTP1.1的扩展协议，提供了PUT、MOVE、DELETE等方法，如果网站目录权限设置不当，攻击者可以利用WebDAV提供的PUT方法直接从远程上传文件。再配合IIS的解析漏洞就可以控制整个网站。 | 如果网站不需要使用相关方法，建议将其关闭。
检查相关目录的权限设置。 |
| 应用配置 | http方法 | B07 | 应用代码 | DELETE方法删除任意文件 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 8.3 | High | 攻击者利用HTTP DELETE方法删除应用程序文件。 | 如果网站不需要使用DELETE方法，建议将其关闭。
检查相关目录的权限设置。 |
| 应用配置 | http方法 | B08 | 应用代码 | 用户名密码GET方法传输 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N | 4.3 | Medium | 用户名密码以GET方式提交，容易在代理或者日志中造成泄露，增加了攻击者获取账号密码的风险 | 使用POST方法传输，用户名密码以POST参数的方式提交，不可出现在url地址中 |
| 应用配置 | http方法 | B09 | 配置部署 | 不安全的HTTP方法 |  | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N | 5.3 | Medium | 可能会在 Web 服务器上上载、修改或删除 Web 页面、脚本和文件（注：Weblogic环境下需要在war中被设置好） | 建议只保留基本HTTP方法GET、POST.如有业务需要开启其它HTTP方法，应做好权限控制。 |
| 应用配置 | 配置错误 | B10 | 应用代码 | 存在安装测试文件 | 存在安装文件 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:H | 7.6 | High | 应用程序中的安装文件，未做访问控制，攻击者可以直接访问这些文件，重新安装应用程序。 | 严格控制安装文件的访问，或在正式服务器上删除。 |
| 应用配置 | 配置错误 | B11 | 应用代码 | 存在敏感文件 | 存在[php.info/存在测试页面，没法造成其他类型漏洞的危害](http://php.info/%E5%AD%98%E5%9C%A8%E6%B5%8B%E8%AF%95%E9%A1%B5%E9%9D%A2%EF%BC%8C%E6%B2%A1%E6%B3%95%E9%80%A0%E6%88%90%E5%85%B6%E4%BB%96%E7%B1%BB%E5%9E%8B%E6%BC%8F%E6%B4%9E%E7%9A%84%E5%8D%B1%E5%AE%B3) | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N | 4.3 | Medium | 应用程序中的部分文件，攻击者可以直接访问这些文件，造成一定的信息泄露或危险操作。 | 删除不必要的测试文件。 |
| 应用配置 | 配置错误 | B12 | 应用代码 | 存在备份文件 | 存在备份文件，xlsx文件，db文件，log文件 | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N | 6.5 | Medium | 系统Web目录下存在备份文件，xlsx文件，db文件，日志文件可被下载，造成相关敏感数据泄露。 | 严格控制相关文件的访问；
如无必要，禁止存放在Web根目录。 |
| 应用配置 | 配置错误 | B13 | 配置部署 | Cookie未启用HttpOnly属性 |  | CVSS:3.0/AV:N/AC:H/PR:H/UI:N/S:U/C:L/I:N/A:N | 2.2 | Low | Cookie未设置HttpOnly属性，攻击者可以通过JS脚本的方式获取到该cookie。（注：Weblogic环境下需要在war中被设置好） | Cookie应设置HttpOnly属性 |
| 应用配置 | 配置错误 | B14 | 配置部署 | Cookie未启用Secure属性 |  | CVSS:3.0/AV:N/AC:H/PR:H/UI:N/S:U/C:L/I:N/A:N | 2.2 | Low | Cookie未设置secure属性，攻击者可以通过嗅探HTTP形式的数据包获取到该cookie。 | Cookie应设置secure属性 |
| 应用配置 | 数据传输 | B15 | 配置部署 | HTTP协议传输敏感信息 | 只要协议是http | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N | 5.3 | Medium | 攻击者通过嗅探HTTP形式的传输数据，获取敏感数据。 | 使用https协议传输敏感信息。 |
| 应用配置 | 配置错误 | B16 | 配置部署 | SSL 版本过低 |  | CVSS:3.0/AV:A/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N | 3.7 | Low | 应用程序采用的ssl协议版本过低，低版本的ssl存在一些安全问题可被攻击者利用. | 采用TLS 1.2以上 |
| 应用配置 | 配置错误 | B17 | 配置部署 | SSL Poodle 信息泄露 |  | CVSS:3.0/AV:A/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N | 3.7 | Low | 攻击者可以利用此漏洞获取SSL通信中的部分明文信息 | 禁用SSLv3协议，使用TLS1.2 以上 |
| 应用配置 | 配置错误 | B18 | 应用代码 | SSL 弱加密算法 |  | CVSS:3.0/AV:A/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:N | 3.7 | Low | SSL协议支持弱加密算法，这将增加攻击者破解加密过程的风险 | 禁用相关弱加密算法 |
| 应用配置 | 配置错误 | B19 | 配置部署 | HOST头攻击 |  | CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:N/A:L | 5 | Medium | 如果应用程序没有对Host字段值进行处理，就有可能造成恶意代码的传入。 | 对Host字段进行检测Nginx，修改ngnix.conf文件，在server中指定一个server_name名单，并添加检测Apache，修改httpd.conf文件，指定ServerName，并开启UseCanonicalName选项。Tomcat，修改server.xml文件，配置Host的name属性。Web应用程序应该使用SERVER_NAME而不是host header。在Apache和Nginx里可以通过设置一个虚拟机来记录所有的非法host header。在Nginx里还可以通过指定一个SERVER_NAME名单，Apache也可以通过指定一个SERVER_NAME名单并开启UseCanonicalName选项。不要使用类似JSP中request.getServerName( )方法引用客户端输入的hostname值。拼接生成URL时引用静态变量定义的服务器域名，或者使用相对路径生成URL。 |
| 中间件 | 中间件 | F01 | 配置部署 | Weblogic SSRF | /uddiexplorer/SearchPublicRegistries.jsp | CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:C/C:H/I:L/A:L | 7.7 | High | Weblogic中间件存在SSRF漏洞，攻击者可以利用此漏洞探测内网资产及端口服务信息，进而造成进一步破坏。 | 1.如果业务不需要UDDI功能，就关闭这个功能。可以删除uddiexporer文件夹，可以可在/weblogicPath/server/lib/uddiexplorer.war解压后，注释掉上面的jsp再打包。2. 升级Weblogic至最新版本 |
| 中间件 | 中间件 | F02 | 配置部署 | Weblogic反序列化远程代码执行漏洞 | 影响组件vbea_wls9_async_response.warwsat.war | CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L | 9.1 | Critical | 由于在反序列化处理输入信息的过程中存在缺陷，未经授权的攻击者可以发送精心构造的恶意 HTTP 请求，利用该漏洞获取服务器权限，实现远程代码执行。 | 1、升级到JDK7u21以上版本
或者2、升级官方CVE-2019-2725补丁包。或者3、删除wls9_async_response.war与wls-wsat.war文件及相关文件夹，并重启Weblogic服务。 |
| 中间件 | 中间件 | F03 | 配置部署 | redis未授权访问 | Redis未授权访问是因为一些Redis服务绑定到公共接口，甚至没有密码身份验证保护。 |  |  | High | 导致任意用户在可以访问此公网的情况下未授权访问Redis以及读取Redis的数据。 | 修改配置禁止未授权访问 |
| 中间件 | 中间件 | F04 | 配置部署 | Apache Tomcat 文件包含漏洞(CVE-2020-1938) | 对于处在漏洞影响版本范围内的 Tomcat 而言，若其开启 AJP Connector 且攻击者能够访问 AJP Connector 服务端口的情况下，即存在被 Ghostcat 漏洞利用的风险。 |  |  | High | Tomcat AJP 协议设计上存在缺陷，攻击者通过 Tomcat AJP Connector 可以读取或包含 Tomcat 上所有 webapp 目录下的任意文件,例如可以读取 webapp 配置文件或源代码。此外在目标应用有文件上传功能的情况下，配合文件包含的利用还可以达到远程代码执行的危害。 | 升级最新版本 |
| 应用框架 | 应用框架 | F05 | 配置部署 | springboot Actuator 未授权访问 | 全版本且无安全配置 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | Actuator默认配置会出现接口未授权访问，
部分接口会泄露网站流量信息和内存信息等，使用Jolokia库特性甚至可以远程执行任意代码，获取服务器权限。 | 禁用所有接口，将配置改成：endpoints.enabled = false，开启security功能，配置访问权限验证 |
| 应用框架 | 应用框架 | F06 | 配置部署 | Druid配置错误导致未授权访问漏洞 | Apache Druid 默认情况下缺乏授权认证 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | druid作为数据库连接池，默认配置监控页存在漏洞，
可以通过直接通过GET /druid/index.html 直接访问，存在数据库数据泄露的风险。泄漏数据库信息 | 在配置文件中禁用druid监控页或添加用户名密码 |
| 应用框架 | 应用框架 | F07 | 应用代码 | Nacos权限认证绕过 | Nacos 默认情况下缺乏授权认证 | CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:N | 8.2 | High | 在 Nacos启用身份验证后，仍然可以利用该漏洞绕过身份验证并访问任何页面。 | 升级最新版本 |
| 应用框架 | 应用框架 | F08 | 应用代码 | log4jshell | 由于Apache Log4j2某些功能存在递归解析功能，攻击者可直接构造恶意请求，触发远程代码执行漏洞。 |  | 10 | Critical | 攻击者利用log4j2反序列化漏洞，直接获取操作系统权限、执行操作系统命令。 | 升级最新版本 |
| 应用框架 | 应用框架 | F09 | 应用代码 | RabbitMQ 未授权方法/默认密码 | 配置不当存在默认账户密码guest guest |  |  | High | 攻击者利用默认账户密码进入后台获取权限 | 升级最新版本 |
| 应用框架 | 应用框架 | F10 | 配置部署 | redis未授权访问 | Redis未授权访问是因为一些Redis服务绑定到公共接口，甚至没有密码身份验证保护。 |  |  | High | 导致任意用户在可以访问此公网的情况下未授权访问Redis以及读取Redis的数据。 | 修改配置禁止未授权访问 |
| 应用框架 | 应用框架 | F11 | 配置部署 | Apache Tomcat 文件包含漏洞(CVE-2020-1938) | 对于处在漏洞影响版本范围内的 Tomcat 而言，若其开启 AJP Connector 且攻击者能够访问 AJP Connector 服务端口的情况下，即存在被 Ghostcat 漏洞利用的风险。 |  |  | High | Tomcat AJP 协议设计上存在缺陷，攻击者通过 Tomcat AJP Connector 可以读取或包含 Tomcat 上所有 webapp 目录下的任意文件,例如可以读取 webapp 配置文件或源代码。此外在目标应用有文件上传功能的情况下，配合文件包含的利用还可以达到远程代码执行的危害。 | 升级最新版本 |
